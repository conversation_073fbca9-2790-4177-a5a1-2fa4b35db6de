This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed, content has been formatted for parsing in plain style, content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: .csv, .old, repomix-output.txt, .cache, .ruff_cache, .repomix, venv, .cursor, .roo, logs, tasks, urls.csv, test.csv, *.csv
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Content has been formatted for parsing in plain style
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
tests/
  test-browser-sim.go
  test-direct-access.go
  test-manual-login.go
.env.example
debug_login.html
go.mod
main.go
test-scraper.sh

================================================================
Files
================================================================

================
File: tests/test-browser-sim.go
================
package main
import (
	"log"
	"os"
	"time"
	"github.com/gocolly/colly"
)
⋮----
"log"
"os"
"time"
"github.com/gocolly/colly"
⋮----
func main()

================
File: tests/test-direct-access.go
================
package main
import (
	"log"
	"os"
	"github.com/gocolly/colly"
)
⋮----
"log"
"os"
"github.com/gocolly/colly"
⋮----
func main()

================
File: tests/test-manual-login.go
================
package main
import (
	"fmt"
	"log"
	"os"
	"time"
	"github.com/gocolly/colly"
)
⋮----
"fmt"
"log"
"os"
"time"
"github.com/gocolly/colly"
⋮----
func main()

================
File: .env.example
================
# Environment variables for Telekom LEB Service scraper
# Copy this file to .env and fill in your actual credentials

# Your Telekom login credentials
TELEKOM_USERNAME=your_actual_username_here
TELEKOM_PASSWORD=your_actual_password_here

# Usage:
# 1. Copy this file: cp .env.example .env
# 2. Edit .env with your real credentials
# 3. Load variables: source .env
# 4. Run scraper: go run main.go

================
File: debug_login.html
================
<!DOCTYPE html>
<html lang="de-DE">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-param" content="_csrf">
<meta name="csrf-token" content="bPuyX8exztEQaYzxae1OiUZqRwDdcqtuCE_nJYqWgA8tn_tprNiM4T08y5lblwLQCCE-c-0a2xR6f5Ro4PLqbQ==">
    <title>
        eVergabe     </title>
    <noscript>
        <style type="text/css">
            .page, .t-systems-logo, .rib-logo, .footer-wrap, .footer {
                display: none;
            }
            .noscript {
                padding: 15px 10px;
                margin: 50px auto 0;
                max-width: 800px;
                border: solid 1px #ddd;
                border-radius: 4px;
                background-color: #ddd;
            }
            .noscript h1, .noscript h2 {
                text-align: center;
            }
        </style>
    </noscript>
    <link href="/assets/44e7ece1/css/bootstrap.css" rel="stylesheet">
<link href="/assets/c268197a/main.css" rel="stylesheet">
</head>
<body>
<noscript>
    <div class="noscript">
        <h1>In Ihrem Browser ist JavaScript deaktiviert.</h1>
        <h2>Um die Funktionen dieser Webapplikation nutzen zu können müssen sie Javascript aktivieren.</h2>
        <h2><a href="/public/login">Seite neu Laden</a></h2>
    </div>
</noscript>
<div class="wrap page">
    <div id="navbar">
        <nav id="w1" class="navbar-fixed-top navbar-default navbar"><div class="container"><div class="navbar-header"><button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#w1-collapse"><span class="sr-only">Toggle navigation</span>
<span class="icon-bar"></span>
<span class="icon-bar"></span>
<span class="icon-bar"></span></button><a class="navbar-brand" href="/">eVergabe<small> Herzlich Willkommen </small></a></div><div id="w1-collapse" class="collapse navbar-collapse"><ul id="w2" class="navbar-nav navbar-right nav"><li class="active"><a href="/public/login"><span class="glyphicon glyphicon-user"></span> Anmelden</a></li>
<li class="dropdown"><a class="dropdown-toggle" href="#" data-toggle="dropdown"><span class="glyphicon glyphicon-menu-hamburger"></span>  <span class="caret"></span></a><ul id="w3" class="dropdown-menu"><li><a href="/public/privacy-policy" tabindex="-1">Datenschutzrichtlinie</a></li>
<li><a href="/public/contact" tabindex="-1">Kontakt</a></li>
<li><a href="/public/about" tabindex="-1">Impressum</a></li>
<li class="divider" style="display:none;"></li>
<li class="dropdown-header" style="display:none;">Einstellungen</li></ul></li></ul></div></div></nav>    </div>
    <div class="container-fluid">
        <div id="subNav">
            <div class="row tcom-brand">
                <div class="col-sm-offset-1 col-sm-2">
                    <div class="tcom-logo"></div>
                </div>
                <div class="col-sm-4 col-sm-offset-4">
                    <div class="slogan pull-right"><span class="text-uppercase">Erleben, was verbindet.</span></div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-sm-offset-1 col-sm-10">
                    <ul id="w4" class="nav-tabs main-menu nav"><li><a href="/public/index"><span class="glyphicon glyphicon-home" title="Startseite"></span></a></li>
<li><a href="/public-publication/index">Bekanntmachungen</a></li>
<li><a href="/public/reset-password">Zugangsdaten vergessen</a></li>
<li><a href="/bauabzugsteuer/index">Bauabzugsteuer</a></li>
<li title="Melden Sie hier Ausfälle."><a href="/public/beeintraechtigungs-anzeige">Beeinträchtigungsanzeige</a></li>
<li title="Auszug der Vertragsbedingungen für die Teilleistungen Tiefbau, Montage &amp; Einbringen."><a href="/public/condition">Vertragsbedingungen</a></li></ul>                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-offset-1 col-sm-10">
                <div id="breadcrumbs">
                    <div class="row">
                        <div class="col-sm-12">
                            <ul class="breadcrumb"><li><a href="/">Home</a></li>
<li class="active">Login</li>
</ul>                        </div>
                    </div>
                                    </div>
                <div class="content">
                    <div class="row">
    <div class="text-center col-sm-12" style="margin-bottom: 50px;">
        <h1>Bitte Loggen Sie sich ein.</h1>
    </div>
    <div class="col-sm-3"></div>
    <div class="col-sm-6">
        <form id="w0" class="form-horizontal" action="/public/login" method="post">
<input type="hidden" name="_csrf" value="bPuyX8exztEQaYzxae1OiUZqRwDdcqtuCE_nJYqWgA8tn_tprNiM4T08y5lblwLQCCE-c-0a2xR6f5Ro4PLqbQ=="><div class="form-group field-loginform-username required">
<label class="control-label col-sm-4" for="loginform-username">Benutzername</label>
<div class="col-sm-8">
<input type="text" id="loginform-username" class="form-control" name="LoginForm[username]" value="hakanekerfiber" aria-required="true">
<p class="help-block help-block-error "></p>
</div>
</div><div class="form-group field-loginform-password required has-error">
<label class="control-label col-sm-4" for="loginform-password">Passwort</label>
<div class="col-sm-8">
<input type="password" id="loginform-password" class="form-control" name="LoginForm[password]" value="det0ymt1YJG@ncz7uz" aria-required="true" aria-invalid="true">
<p class="help-block help-block-error ">Falscher Benutzername oder Passwort</p>
</div>
</div><div class="form-group" style="text-align: right;">
    <div class="col-sm-12">
        <button type="submit" class="btn btn-success">Login</button>    </div>
</div>
</form>
    </div>
    <div class="col-sm-3"></div>
</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="footer-wrap">
    <footer class="footer">
        <div class="row">
            <div class="col-sm-10 col-sm-offset-1">
                <p style="text-align: right; padding-top: 80px; display: inline-block">Version: 4b8e6096</p>
                <div class="pull-right" style="width: 200px; display: inline-block; ">
                    <div class="rib-logo"></div>
                    <div class="pull-right" style="text-align: right;">Copyright &copy; 2025 by
                                                                       RIB.<br/> All Rights Reserved.<br/></div>
                </div>
                <div class="pull-right" style="width: 200px; display: inline-block; margin-left: 15px; ">
                    <div class="t-systems-logo"></div>
                    <div class="pull-right" style="text-align: right;">Copyright &copy; 2025 by
                                                                       Telekom IT GmbH.<br/> All Rights
                                                                       Reserved.<br/>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>
<script src="/assets/a84aab3f/jquery.js"></script>
<script src="/assets/55b8a111/yii.js"></script>
<script src="/assets/55b8a111/yii.validation.js"></script>
<script src="/assets/55b8a111/yii.activeForm.js"></script>
<script src="/assets/44e7ece1/js/bootstrap.js"></script>
<script src="/assets/4dc69c83/js/prompt.js"></script>
<script src="/assets/c52ccb9c/loader.js"></script>
<script src="/assets/c52ccb9c/jquery.top-link.js"></script>
<script src="/assets/c52ccb9c/utility.evergabe.js"></script>
<script src="/assets/b45f5dd8/js/prevent-double-submit.js"></script>
<script>jQuery(function ($) {
jQuery('#w0').yiiActiveForm([{"id":"loginform-username","name":"username","container":".field-loginform-username","input":"#loginform-username","error":".help-block.help-block-error","validate":function (attribute, value, messages, deferred, $form) {yii.validation.required(value, messages, {"message":"Benutzername darf nicht leer sein."});yii.validation.string(value, messages, {"message":"Benutzername muss eine Zeichenkette sein.","skipOnEmpty":1});}},{"id":"loginform-password","name":"password","container":".field-loginform-password","input":"#loginform-password","error":".help-block.help-block-error","validate":function (attribute, value, messages, deferred, $form) {yii.validation.required(value, messages, {"message":"Passwort darf nicht leer sein."});yii.validation.string(value, messages, {"message":"Passwort muss eine Zeichenkette sein.","skipOnEmpty":1});}}], []);
});</script><div class="loader"></div>
<script>
        if (typeof jQuery != "function") {
        alert("JQuery konnte nicht geladen werden. Es könnte sein das Ihr Browser die benötigte JQuery-Version nicht unterstüzt. Es wird mind. JQuery 2.x benötigt. (Internet Explorert 8 oder geringer werden nicht unterstüzt.) Wenn das Problem trotzdem auftritt wenden Sie sich bitte an den Support.");
    }
</script>
</body>
</html>

================
File: go.mod
================
module TelekomLEBService

go 1.24.3

require (
	github.com/PuerkitoBio/goquery v1.10.3 // indirect
	github.com/andybalholm/cascadia v1.3.3 // indirect
	github.com/antchfx/htmlquery v1.3.4 // indirect
	github.com/antchfx/xmlquery v1.4.4 // indirect
	github.com/antchfx/xpath v1.3.3 // indirect
	github.com/gobwas/glob v0.2.3 // indirect
	github.com/gocolly/colly v1.2.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/joho/godotenv v1.5.1 // indirect
	github.com/kennygrant/sanitize v1.2.4 // indirect
	github.com/saintfish/chardet v0.0.0-20230101081208-5e3ef4b5456d // indirect
	github.com/temoto/robotstxt v1.1.2 // indirect
	golang.org/x/net v0.41.0 // indirect
	golang.org/x/text v0.26.0 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/protobuf v1.26.0 // indirect
)

================
File: main.go
================
package main
import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"
	"time"
	"github.com/gocolly/colly"
	"github.com/joho/godotenv"
)
⋮----
"encoding/json"
"fmt"
"log"
"os"
"sort"
"strings"
"time"
"github.com/gocolly/colly"
"github.com/joho/godotenv"
⋮----
var loginAttempted bool
func min(a, b int) int
func saveHTMLForDebugging(filename string, content []byte)
type Row struct {
	Kurztext          string `json:"kurztext"`
	Belegnummer       string `json:"belegnummer"`
	Belegeingang      string `json:"belegeingang"`
	Ausfuehrungsfrist string `json:"ausfuehrungsfrist"`
	ZugehoerigerRV    string `json:"zug_rv"`
	Status            string `json:"status"`
}
func main()
⋮----
var keys []string

================
File: test-scraper.sh
================
echo "🧪 Telekom LEB Service Scraper Test Script"
echo "=========================================="
echo ""
echo "1️⃣ Testing without credentials (safe test):"
echo "   This will show the scraper flow but fail at login"
echo ""
go run main.go
echo ""
echo "2️⃣ To test with real credentials, set environment variables:"
echo ""
echo "   export TELEKOM_USERNAME='your_actual_username'"
echo "   export TELEKOM_PASSWORD='your_actual_password'"
echo "   go run main.go"
echo ""
echo "   OR run in one line:"
echo "   TELEKOM_USERNAME='your_username' TELEKOM_PASSWORD='your_password' go run main.go"
echo ""
echo "3️⃣ Alternative: Create a .env file (see .env.example)"




================================================================
End of Codebase
================================================================
