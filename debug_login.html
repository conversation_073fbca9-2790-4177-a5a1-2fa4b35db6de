<!DOCTYPE html>

<html lang="de-DE">
<head>
    <!-- der Tag  http-equiv="X-UA-Compatible" muss an erster stelle stehen -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-param" content="_csrf">
<meta name="csrf-token" content="bPuyX8exztEQaYzxae1OiUZqRwDdcqtuCE_nJYqWgA8tn_tprNiM4T08y5lblwLQCCE-c-0a2xR6f5Ro4PLqbQ==">
    <title>
        eVergabe     </title>

    <noscript>
        <style type="text/css">
            .page, .t-systems-logo, .rib-logo, .footer-wrap, .footer {
                display: none;
            }

            .noscript {
                padding: 15px 10px;
                margin: 50px auto 0;
                max-width: 800px;
                border: solid 1px #ddd;
                border-radius: 4px;
                background-color: #ddd;
            }

            .noscript h1, .noscript h2 {
                text-align: center;
            }
        </style>
    </noscript>

    <link href="/assets/44e7ece1/css/bootstrap.css" rel="stylesheet">
<link href="/assets/c268197a/main.css" rel="stylesheet">
<!--[if lte IE9]>
<script src="/assets/cf31f931/html5shiv/dist/html5shiv.js"></script>
<![endif]-->
<!--[if lte IE9]>
<script src="/assets/cf31f931/respond/dest/respond.min.js"></script>
<![endif]--></head>
<body>
<noscript>
    <div class="noscript">
        <h1>In Ihrem Browser ist JavaScript deaktiviert.</h1>

        <h2>Um die Funktionen dieser Webapplikation nutzen zu können müssen sie Javascript aktivieren.</h2>

        <h2><a href="/public/login">Seite neu Laden</a></h2>
    </div>
</noscript>
<div class="wrap page">
    <div id="navbar">
        <nav id="w1" class="navbar-fixed-top navbar-default navbar"><div class="container"><div class="navbar-header"><button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#w1-collapse"><span class="sr-only">Toggle navigation</span>
<span class="icon-bar"></span>
<span class="icon-bar"></span>
<span class="icon-bar"></span></button><a class="navbar-brand" href="/">eVergabe<small> Herzlich Willkommen </small></a></div><div id="w1-collapse" class="collapse navbar-collapse"><ul id="w2" class="navbar-nav navbar-right nav"><li class="active"><a href="/public/login"><span class="glyphicon glyphicon-user"></span> Anmelden</a></li>
<li class="dropdown"><a class="dropdown-toggle" href="#" data-toggle="dropdown"><span class="glyphicon glyphicon-menu-hamburger"></span>  <span class="caret"></span></a><ul id="w3" class="dropdown-menu"><li><a href="/public/privacy-policy" tabindex="-1">Datenschutzrichtlinie</a></li>
<li><a href="/public/contact" tabindex="-1">Kontakt</a></li>
<li><a href="/public/about" tabindex="-1">Impressum</a></li>
<li class="divider" style="display:none;"></li>
<li class="dropdown-header" style="display:none;">Einstellungen</li></ul></li></ul></div></div></nav>    </div>
    <div class="container-fluid">
        <div id="subNav">
            <div class="row tcom-brand">
                <div class="col-sm-offset-1 col-sm-2">
                    <div class="tcom-logo"></div>
                </div>
                <div class="col-sm-4 col-sm-offset-4">
                    <div class="slogan pull-right"><span class="text-uppercase">Erleben, was verbindet.</span></div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-sm-offset-1 col-sm-10">
                    <ul id="w4" class="nav-tabs main-menu nav"><li><a href="/public/index"><span class="glyphicon glyphicon-home" title="Startseite"></span></a></li>
<li><a href="/public-publication/index">Bekanntmachungen</a></li>
<li><a href="/public/reset-password">Zugangsdaten vergessen</a></li>
<li><a href="/bauabzugsteuer/index">Bauabzugsteuer</a></li>
<li title="Melden Sie hier Ausfälle."><a href="/public/beeintraechtigungs-anzeige">Beeinträchtigungsanzeige</a></li>
<li title="Auszug der Vertragsbedingungen für die Teilleistungen Tiefbau, Montage &amp; Einbringen."><a href="/public/condition">Vertragsbedingungen</a></li></ul>                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-offset-1 col-sm-10">
                <div id="breadcrumbs">
                    <div class="row">
                        <div class="col-sm-12">

                            <ul class="breadcrumb"><li><a href="/">Home</a></li>
<li class="active">Login</li>
</ul>                        </div>
                    </div>
                                    </div>
                <div class="content">
                    <div class="row">
    <div class="text-center col-sm-12" style="margin-bottom: 50px;">
        <h1>Bitte Loggen Sie sich ein.</h1>
    </div>
    <div class="col-sm-3"></div>
    <div class="col-sm-6">
        <form id="w0" class="form-horizontal" action="/public/login" method="post">
<input type="hidden" name="_csrf" value="bPuyX8exztEQaYzxae1OiUZqRwDdcqtuCE_nJYqWgA8tn_tprNiM4T08y5lblwLQCCE-c-0a2xR6f5Ro4PLqbQ=="><div class="form-group field-loginform-username required">
<label class="control-label col-sm-4" for="loginform-username">Benutzername</label>
<div class="col-sm-8">
<input type="text" id="loginform-username" class="form-control" name="LoginForm[username]" value="hakanekerfiber" aria-required="true">

<p class="help-block help-block-error "></p>
</div>
</div><div class="form-group field-loginform-password required has-error">
<label class="control-label col-sm-4" for="loginform-password">Passwort</label>
<div class="col-sm-8">
<input type="password" id="loginform-password" class="form-control" name="LoginForm[password]" value="det0ymt1YJG@ncz7uz" aria-required="true" aria-invalid="true">

<p class="help-block help-block-error ">Falscher Benutzername oder Passwort</p>
</div>
</div><div class="form-group" style="text-align: right;">
    <div class="col-sm-12">
        <button type="submit" class="btn btn-success">Login</button>    </div>
</div>
</form>




    </div>
    <div class="col-sm-3"></div>
</div>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="footer-wrap">
    <footer class="footer">
        <div class="row">
            <div class="col-sm-10 col-sm-offset-1">
                <p style="text-align: right; padding-top: 80px; display: inline-block">Version: 4b8e6096</p>

                <div class="pull-right" style="width: 200px; display: inline-block; ">
                    <div class="rib-logo"></div>
                    <div class="pull-right" style="text-align: right;">Copyright &copy; 2025 by
                                                                       RIB.<br/> All Rights Reserved.<br/></div>
                </div>
                <div class="pull-right" style="width: 200px; display: inline-block; margin-left: 15px; ">
                    <div class="t-systems-logo"></div>
                    <div class="pull-right" style="text-align: right;">Copyright &copy; 2025 by
                                                                       Telekom IT GmbH.<br/> All Rights
                                                                       Reserved.<br/>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

<script src="/assets/a84aab3f/jquery.js"></script>
<script src="/assets/55b8a111/yii.js"></script>
<script src="/assets/55b8a111/yii.validation.js"></script>
<script src="/assets/55b8a111/yii.activeForm.js"></script>
<script src="/assets/44e7ece1/js/bootstrap.js"></script>
<script src="/assets/4dc69c83/js/prompt.js"></script>
<script src="/assets/c52ccb9c/loader.js"></script>
<script src="/assets/c52ccb9c/jquery.top-link.js"></script>
<script src="/assets/c52ccb9c/utility.evergabe.js"></script>
<script src="/assets/b45f5dd8/js/prevent-double-submit.js"></script>
<script>jQuery(function ($) {
jQuery('#w0').yiiActiveForm([{"id":"loginform-username","name":"username","container":".field-loginform-username","input":"#loginform-username","error":".help-block.help-block-error","validate":function (attribute, value, messages, deferred, $form) {yii.validation.required(value, messages, {"message":"Benutzername darf nicht leer sein."});yii.validation.string(value, messages, {"message":"Benutzername muss eine Zeichenkette sein.","skipOnEmpty":1});}},{"id":"loginform-password","name":"password","container":".field-loginform-password","input":"#loginform-password","error":".help-block.help-block-error","validate":function (attribute, value, messages, deferred, $form) {yii.validation.required(value, messages, {"message":"Passwort darf nicht leer sein."});yii.validation.string(value, messages, {"message":"Passwort muss eine Zeichenkette sein.","skipOnEmpty":1});}}], []);
});</script><div class="loader"></div>

<script>
        if (typeof jQuery != "function") {
        alert("JQuery konnte nicht geladen werden. Es könnte sein das Ihr Browser die benötigte JQuery-Version nicht unterstüzt. Es wird mind. JQuery 2.x benötigt. (Internet Explorert 8 oder geringer werden nicht unterstüzt.) Wenn das Problem trotzdem auftritt wenden Sie sich bitte an den Support.");
    }
</script>
</body>
</html>




