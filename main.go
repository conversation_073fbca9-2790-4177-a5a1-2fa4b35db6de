package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/gocolly/colly"
	"github.com/joho/godotenv"
)

var loginAttempted bool

// Helper functions
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func saveHTMLForDebugging(filename string, content []byte) {
	if err := os.WriteFile(filename, content, 0644); err != nil {
		log.Printf("⚠️ Could not save HTML debug file: %v", err)
	} else {
		log.Printf("💾 Saved HTML debug file: %s", filename)
	}
}

type Row struct {
	Kurztext          string `json:"kurztext"`
	Belegnummer       string `json:"belegnummer"`
	Belegeingang      string `json:"belegeingang"`
	Ausfuehrungsfrist string `json:"ausfuehrungsfrist"`
	ZugehoerigerRV    string `json:"zug_rv"`
	Status            string `json:"status"`
}

func main() {
	log.Println("🚀 Starting Telekom LEB Service scraper...")

	// Load .env file
	if err := godotenv.Load(); err != nil {
		log.Printf("⚠️ Warning: Could not load .env file: %v", err)
	}

	// Get credentials from environment variables
	username := os.Getenv("TELEKOM_USERNAME")
	password := os.Getenv("TELEKOM_PASSWORD")

	if username == "" || password == "" {
		log.Fatal("🚫 TELEKOM_USERNAME and TELEKOM_PASSWORD must be set as environment variables")
	}
	log.Printf("✅ Using credentials from environment variables")

	// 1. Grund-collector with enhanced debugging
	c := colly.NewCollector(
		colly.AllowedDomains("www.evergabe.telekom.de"),
		colly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
	)
	c.AllowURLRevisit = true // behåll cookies

	// Enhanced request handling with proper headers
	c.OnRequest(func(r *colly.Request) {
		log.Printf("🌐 Visiting: %s", r.URL.String())

		// Add realistic browser headers
		r.Headers.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
		r.Headers.Set("Accept-Language", "de-DE,de;q=0.9,en;q=0.8")
		r.Headers.Set("Accept-Encoding", "gzip, deflate, br")
		r.Headers.Set("DNT", "1")
		r.Headers.Set("Connection", "keep-alive")
		r.Headers.Set("Upgrade-Insecure-Requests", "1")

		// Set Referer for login requests
		if strings.Contains(r.URL.Path, "login") && r.Method == "POST" {
			r.Headers.Set("Referer", "https://www.evergabe.telekom.de/public/login")
			r.Headers.Set("Origin", "https://www.evergabe.telekom.de")
			r.Headers.Set("Content-Type", "application/x-www-form-urlencoded")
		}

		log.Printf("🍪 Request headers: %v", r.Headers)

		// Add small delay to mimic human behavior
		time.Sleep(500 * time.Millisecond)
	})

	// Debug: Enhanced response logging
	c.OnResponse(func(r *colly.Response) {
		log.Printf("📄 Response from %s: Status %d, Size: %d bytes",
			r.Request.URL.String(), r.StatusCode, len(r.Body))
		log.Printf("🍪 Response cookies: %v", r.Headers.Get("Set-Cookie"))

		// Save HTML files for debugging
		if r.Request.URL.Path == "/public/login" || r.Request.URL.Path == "/framework-agreement-call/index" {
			filename := fmt.Sprintf("debug_%s_%d.html",
				r.Request.URL.Path[1:], r.StatusCode) // Remove leading slash
			filename = fmt.Sprintf("debug_%s.html",
				r.Request.URL.Path[strings.LastIndex(r.Request.URL.Path, "/")+1:])
			saveHTMLForDebugging(filename, r.Body)
		}

		// Log response body if it's small (for debugging login forms)
		if len(r.Body) < 20000 {
			log.Printf("� Response body preview (first 500 chars): %s...",
				string(r.Body)[:min(500, len(r.Body))])
		}
	})

	// Debug: Log errors
	c.OnError(func(r *colly.Response, err error) {
		log.Printf("❌ Error on %s: %v (Status: %d)",
			r.Request.URL.String(), err, r.StatusCode)
	})

	// Log any explicit login error shown by Yii2
	c.OnHTML("div.alert-danger", func(e *colly.HTMLElement) {
		msg := strings.TrimSpace(e.Text)
		if msg != "" {
			log.Printf("🔴 Login error shown by portal (alert-danger): %s", msg)
		}
	})

	// Also check for form field errors
	c.OnHTML("p.help-block-error", func(e *colly.HTMLElement) {
		msg := strings.TrimSpace(e.Text)
		if msg != "" {
			log.Printf("🔴 Login error shown by portal (help-block-error): %s", msg)
		}
	})

	// 2. Kontroll om vi måste logga in
	c.OnHTML("body", func(e *colly.HTMLElement) {
		tableCount := e.DOM.Find("table.table").Length()
		log.Printf("🔍 Found %d tables with class 'table'", tableCount)

		if tableCount == 0 {
			if loginAttempted {
				log.Fatal("🚫 Login failed or wrong credentials. Aborting further attempts.")
				return
			}
			loginAttempted = true
			log.Println("🔐 No data table found - inspecting login page")

			// Enhanced form inspection
			log.Println("📝 Analyzing all forms on the page:")
			e.ForEach("form", func(i int, form *colly.HTMLElement) {
				action, _ := form.DOM.Attr("action")
				method, _ := form.DOM.Attr("method")
				log.Printf("   Form %d: action='%s', method='%s'", i+1, action, method)

				// Log all input fields in this form
				form.ForEach("input", func(j int, input *colly.HTMLElement) {
					name, _ := input.DOM.Attr("name")
					inputType, _ := input.DOM.Attr("type")
					value, _ := input.DOM.Attr("value")
					placeholder, _ := input.DOM.Attr("placeholder")
					log.Printf("      Input %d: name='%s', type='%s', value='%s', placeholder='%s'",
						j+1, name, inputType, value, placeholder)
				})
			})

			// 2.a Enhanced form data collection
			formAction := e.ChildAttr("form", "action")
			if formAction == "" {
				formAction = "/public/login"
			}
			postURL := e.Request.AbsoluteURL(formAction)

			// Collect ALL hidden fields automatically
			payload := make(map[string]string)

			// Add all hidden input fields
			e.ForEach("form input[type='hidden']", func(i int, input *colly.HTMLElement) {
				name, _ := input.DOM.Attr("name")
				value, _ := input.DOM.Attr("value")
				if name != "" {
					payload[name] = value
					log.Printf("🔒 Found hidden field: %s = %s", name, value)
				}
			})

			// Add username and password (only the required fields)
			payload["LoginForm[username]"] = username
			payload["LoginForm[password]"] = password

			log.Printf("🔑 Submitting login form to %s", postURL)

			log.Printf("📤 Login payload being sent (sorted):")
			var keys []string
			for k := range payload {
				keys = append(keys, k)
			}
			sort.Strings(keys)
			for _, k := range keys {
				if k == "LoginForm[password]" {
					log.Printf("   %s: [REDACTED len=%d]", k, len(payload[k]))
				} else if k == "LoginForm[username]" {
					log.Printf("   %s: [REDACTED len=%d]", k, len(payload[k]))
				} else {
					log.Printf("   %s: %s", k, payload[k])
				}
			}

			// Add delay before login attempt to mimic human behavior
			log.Println("⏳ Waiting 2 seconds before login attempt...")
			time.Sleep(2 * time.Second)

			if err := c.Post(postURL, payload); err != nil {
				log.Fatalf("❌ Login request failed: %v", err)
			}

			log.Println("✅ Login form submitted successfully")

			// Revisit the protected page *only once*, after allowing backend a moment
			go func() {
				time.Sleep(800 * time.Millisecond)
				if err := c.Visit("https://www.evergabe.telekom.de/framework-agreement-call/index"); err != nil {
					log.Printf("❌ Failed to revisit target page after login: %v", err)
				}
			}()
		} else {
			log.Println("✅ Data table found - proceeding with scraping")
		}
	})

	// 3. Samla ihop raderna
	rows := make([]Row, 0, 64)
	c.OnHTML("table.table tbody tr", func(e *colly.HTMLElement) {
		row := Row{
			Kurztext:          e.ChildText("td:nth-child(3)"),
			Belegnummer:       e.ChildText("td:nth-child(4)"),
			Belegeingang:      e.ChildText("td:nth-child(5)"),
			Ausfuehrungsfrist: e.ChildText("td:nth-child(6)"),
			ZugehoerigerRV:    e.ChildText("td:nth-child(7)"),
			Status:            e.ChildText("td:nth-child(8)"),
		}
		rows = append(rows, row)
		log.Printf("📊 Scraped row %d: %s", len(rows), row.Belegnummer)
	})

	// 4. Kör!
	log.Println("🎯 Starting scrape of target URL...")
	if err := c.Visit("https://www.evergabe.telekom.de/framework-agreement-call/index"); err != nil {
		log.Printf("❌ Failed to visit target URL: %v", err)
		log.Fatal(err)
	}

	// 5. Skriv JSON
	log.Printf("✅ Scraping complete! Found %d rows", len(rows))
	out, _ := json.MarshalIndent(rows, "", "  ")
	log.Println("📋 Results:")
	log.Println(string(out))
}
