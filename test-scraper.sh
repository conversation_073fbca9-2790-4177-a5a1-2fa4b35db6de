#!/bin/bash

# Test script for Telekom LEB Service scraper
echo "🧪 Telekom LEB Service Scraper Test Script"
echo "=========================================="

# Method 1: Test without credentials (will show warning but demonstrate flow)
echo ""
echo "1️⃣ Testing without credentials (safe test):"
echo "   This will show the scraper flow but fail at login"
echo ""
go run main.go

echo ""
echo "2️⃣ To test with real credentials, set environment variables:"
echo ""
echo "   export TELEKOM_USERNAME='your_actual_username'"
echo "   export TELEKOM_PASSWORD='your_actual_password'"
echo "   go run main.go"
echo ""
echo "   OR run in one line:"
echo "   TELEKOM_USERNAME='your_username' TELEKOM_PASSWORD='your_password' go run main.go"
echo ""

echo "3️⃣ Alternative: Create a .env file (see .env.example)"
