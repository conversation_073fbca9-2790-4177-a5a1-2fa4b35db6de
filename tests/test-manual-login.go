package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gocolly/colly"
)

func main() {
	username := os.Getenv("TELEKOM_USERNAME")
	password := os.Getenv("TELEKOM_PASSWORD")

	if username == "" || password == "" {
		log.Fatal("Please set TELEKOM_USERNAME and TELEKOM_PASSWORD environment variables")
	}

	log.Println("🧪 Manual Login Test - Step by Step")
	
	c := colly.NewCollector(
		colly.AllowedDomains("www.evergabe.telekom.de"),
		colly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
	)

	// Step 1: Visit login page and inspect
	log.Println("📋 Step 1: Visiting login page...")
	c.OnHTML("body", func(e *colly.HTMLElement) {
		// Check if we're on login page
		if e.DOM.Find("input[name='LoginForm[username]']").Length() > 0 {
			log.Println("✅ Found login form")
			
			// Extract all form data
			csrfToken := e.ChildAttr("input[name='_csrf']", "value")
			log.Printf("🔑 CSRF Token: %s", csrfToken)
			
			// Try alternative login approach
			log.Println("🔄 Attempting login with different method...")
			
			// Use form submission instead of Post
			form := e.DOM.Find("form").First()
			action, _ := form.Attr("action")
			method, _ := form.Attr("method")
			
			log.Printf("📝 Form action: %s, method: %s", action, method)
			
			// Manual form data preparation
			formData := map[string]string{
				"_csrf":               csrfToken,
				"LoginForm[username]": username,
				"LoginForm[password]": password,
			}
			
			log.Println("📤 Submitting with manual form data...")
			for k, v := range formData {
				if k == "LoginForm[password]" {
					log.Printf("   %s: [HIDDEN]", k)
				} else {
					log.Printf("   %s: %s", k, v)
				}
			}
			
			// Submit form
			if err := c.Post(e.Request.AbsoluteURL(action), formData); err != nil {
				log.Printf("❌ Form submission failed: %v", err)
			}
		} else {
			log.Println("✅ Not on login page - checking for data...")
			tables := e.DOM.Find("table.table").Length()
			log.Printf("📊 Found %d data tables", tables)
			
			if tables > 0 {
				log.Println("🎉 SUCCESS! Login worked and we found data tables!")
			} else {
				log.Println("❓ No data tables found, but not on login page either")
			}
		}
	})

	// Visit the login page
	if err := c.Visit("https://www.evergabe.telekom.de/public/login"); err != nil {
		log.Fatalf("❌ Failed to visit login page: %v", err)
	}

	log.Println("🏁 Test completed")
}
