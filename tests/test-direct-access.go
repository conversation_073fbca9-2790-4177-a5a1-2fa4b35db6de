package main

import (
	"log"
	"os"

	"github.com/gocolly/colly"
)

func main() {
	log.Println("🧪 Testing Direct Access to Target Page")

	c := colly.NewCollector(
		colly.AllowedDomains("www.evergabe.telekom.de"),
		colly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
	)

	c.OnResponse(func(r *colly.Response) {
		log.Printf("📄 Response: %d from %s", r.StatusCode, r.Request.URL.String())
		log.Printf("📏 Content size: %d bytes", len(r.Body))
	})

	c.OnHTML("body", func(e *colly.HTMLElement) {
		// Check what we got
		tables := e.DOM.Find("table.table").Length()
		log.Printf("📊 Found %d tables with class 'table'", tables)

		forms := e.DOM.Find("form").Length()
		log.Printf("📝 Found %d forms", forms)

		loginInputs := e.DOM.Find("input[name='LoginForm[username]']").Length()
		if loginInputs > 0 {
			log.Println("🔐 Redirected to login page")
		} else if tables > 0 {
			log.Println("🎉 SUCCESS! Found data tables without login!")

			// Try to scrape some data
			e.ForEach("table.table tbody tr", func(i int, row *colly.HTMLElement) {
				if i < 3 { // Just show first 3 rows
					log.Printf("📋 Row %d data:", i+1)
					row.ForEach("td", func(j int, cell *colly.HTMLElement) {
						text := cell.Text
						if len(text) > 50 {
							text = text[:50] + "..."
						}
						log.Printf("   Cell %d: %s", j+1, text)
					})
				}
			})
		} else {
			log.Println("❓ Unknown page content")
			// Save for inspection
			if err := os.WriteFile("direct_access_debug.html", e.Response.Body, 0644); err == nil {
				log.Println("💾 Saved page content to direct_access_debug.html")
			}
		}
	})

	// Try to access the target page directly
	log.Println("🎯 Attempting direct access to target page...")
	if err := c.Visit("https://www.evergabe.telekom.de/framework-agreement-call/index"); err != nil {
		log.Printf("❌ Failed to access target page: %v", err)
	}

	log.Println("🏁 Direct access test completed")
}
