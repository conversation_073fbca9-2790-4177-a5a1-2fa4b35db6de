package main

import (
	"log"
	"os"
	"time"

	"github.com/gocolly/colly"
)

func main() {
	username := os.Getenv("TELEKOM_USERNAME")
	password := os.Getenv("TELEKOM_PASSWORD")

	if username == "" || password == "" {
		log.Fatal("Please set TELEKOM_USERNAME and TELEKOM_PASSWORD environment variables")
	}

	log.Println("🤖 Advanced Browser Simulation Test")

	c := colly.NewCollector(
		colly.AllowedDomains("www.evergabe.telekom.de"),
		colly.UserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
	)

	// Simulate more realistic browser behavior
	c.OnRequest(func(r *colly.Request) {
		// Add comprehensive browser headers
		r.Headers.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
		r.Headers.Set("Accept-Language", "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7")
		r.Headers.Set("Accept-Encoding", "gzip, deflate, br")
		r.Headers.Set("Cache-Control", "max-age=0")
		r.Headers.Set("Sec-Ch-Ua", `"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"`)
		r.Headers.Set("Sec-Ch-Ua-Mobile", "?0")
		r.Headers.Set("Sec-Ch-Ua-Platform", `"macOS"`)
		r.Headers.Set("Sec-Fetch-Dest", "document")
		r.Headers.Set("Sec-Fetch-Mode", "navigate")
		r.Headers.Set("Sec-Fetch-Site", "none")
		r.Headers.Set("Sec-Fetch-User", "?1")
		r.Headers.Set("Upgrade-Insecure-Requests", "1")

		// For POST requests (login)
		if r.Method == "POST" {
			r.Headers.Set("Content-Type", "application/x-www-form-urlencoded")
			r.Headers.Set("Origin", "https://www.evergabe.telekom.de")
			r.Headers.Set("Referer", "https://www.evergabe.telekom.de/public/login")
			r.Headers.Set("Sec-Fetch-Dest", "document")
			r.Headers.Set("Sec-Fetch-Mode", "navigate")
			r.Headers.Set("Sec-Fetch-Site", "same-origin")
			r.Headers.Set("Sec-Fetch-User", "?1")
		}

		log.Printf("🌐 %s %s", r.Method, r.URL.String())
	})

	c.OnResponse(func(r *colly.Response) {
		log.Printf("📄 %d from %s (%d bytes)", r.StatusCode, r.Request.URL.String(), len(r.Body))
	})

	// Step 1: Visit login page first (like a real user)
	log.Println("👤 Step 1: Visiting login page like a human...")
	time.Sleep(1 * time.Second)

	c.OnHTML("form", func(e *colly.HTMLElement) {
		action, _ := e.DOM.Attr("action")
		if action == "/public/login" {
			log.Println("📝 Found login form, waiting before filling...")
			time.Sleep(2 * time.Second) // Simulate reading the page

			// Get CSRF token
			csrfToken := e.ChildAttr("input[name='_csrf']", "value")
			log.Printf("🔑 Got CSRF token: %s", csrfToken[:20]+"...")

			// Simulate typing delay
			log.Println("⌨️  Simulating typing...")
			time.Sleep(3 * time.Second)

			// Prepare form data
			formData := map[string]string{
				"_csrf":               csrfToken,
				"LoginForm[username]": username,
				"LoginForm[password]": password,
			}

			log.Println("🔐 Submitting login form...")
			if err := c.Post("https://www.evergabe.telekom.de/public/login", formData); err != nil {
				log.Printf("❌ Login failed: %v", err)
			}
		}
	})

	// Check for successful login
	c.OnHTML("table.table", func(e *colly.HTMLElement) {
		log.Println("🎉 SUCCESS! Found data table - login worked!")
		rows := e.DOM.Find("tbody tr").Length()
		log.Printf("📊 Found %d data rows", rows)
	})

	// Start the process
	if err := c.Visit("https://www.evergabe.telekom.de/public/login"); err != nil {
		log.Fatalf("❌ Failed to start: %v", err)
	}

	log.Println("🏁 Browser simulation test completed")
}
